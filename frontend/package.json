{"name": "task-management-frontend", "version": "0.1.0", "description": "Vue.js frontend for task management system", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest"}, "dependencies": {"vue": "^3.5.17", "vue-router": "^4.4.5", "pinia": "^2.2.6", "axios": "^1.7.9"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "vite": "^7.0.4", "vitest": "^2.1.8"}}