<template>
  <BaseModal
    v-model="isOpen"
    :title="isEditing ? 'Edit User' : 'Create User'"
    size="lg"
    @close="handleClose"
  >
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Personal Information -->
      <div>
        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">
          Personal Information
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <BaseInput
            v-model="form.firstName"
            label="First Name"
            placeholder="Enter first name"
            required
            :error-message="errors.firstName"
          />
          <BaseInput
            v-model="form.lastName"
            label="Last Name"
            placeholder="Enter last name"
            required
            :error-message="errors.lastName"
          />
        </div>
      </div>

      <!-- Account Information -->
      <div>
        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">
          Account Information
        </h4>
        <div class="space-y-4">
          <BaseInput
            v-model="form.username"
            label="Username"
            placeholder="Enter username"
            required
            :error-message="errors.username"
          />
          <BaseInput
            v-model="form.email"
            type="email"
            label="Email"
            placeholder="Enter email address"
            required
            :error-message="errors.email"
          />
          <div v-if="!isEditing" class="space-y-4">
            <BaseInput
              v-model="form.password"
              type="password"
              label="Password"
              placeholder="Enter password"
              required
              :error-message="errors.password"
            />
            <BaseInput
              v-model="form.confirmPassword"
              type="password"
              label="Confirm Password"
              placeholder="Confirm password"
              required
              :error-message="errors.confirmPassword"
            />
          </div>
        </div>
      </div>

      <!-- Role and Status -->
      <div>
        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">
          Role and Status
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="form-label">Role</label>
            <select
              v-model="form.role"
              class="form-input"
              required
            >
              <option value="">Select role</option>
              <option value="user">User</option>
              <option value="manager">Manager</option>
              <option value="admin">Admin</option>
            </select>
            <p v-if="errors.role" class="form-error">
              {{ errors.role }}
            </p>
          </div>
          <div>
            <label class="form-label">Status</label>
            <select
              v-model="form.status"
              class="form-input"
              required
            >
              <option value="">Select status</option>
              <option value="active">Active</option>
              <option value="suspended">Suspended</option>
              <option value="deleted">Deleted</option>
            </select>
            <p v-if="errors.status" class="form-error">
              {{ errors.status }}
            </p>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <BaseAlert
        v-if="submitError"
        variant="danger"
        :message="submitError"
        closable
        @close="submitError = ''"
      />
    </form>

    <template #footer>
      <BaseButton
        variant="outline"
        @click="handleClose"
      >
        Cancel
      </BaseButton>
      <BaseButton
        variant="primary"
        :loading="loading"
        @click="handleSubmit"
      >
        {{ isEditing ? 'Update User' : 'Create User' }}
      </BaseButton>
    </template>
  </BaseModal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useToast } from 'vue-toastification'
import { BaseModal, BaseInput, BaseButton, BaseAlert } from '@/components/ui'
import { apiService } from '@/services/api'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  user: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'user-created', 'user-updated'])

const toast = useToast()

// Reactive state
const loading = ref(false)
const submitError = ref('')

const form = reactive({
  firstName: '',
  lastName: '',
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  role: '',
  status: 'active'
})

const errors = reactive({
  firstName: '',
  lastName: '',
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  role: '',
  status: ''
})

// Computed properties
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEditing = computed(() => !!props.user)

// Methods
const resetForm = () => {
  Object.keys(form).forEach(key => {
    form[key] = ''
  })
  form.status = 'active'
  
  Object.keys(errors).forEach(key => {
    errors[key] = ''
  })
  
  submitError.value = ''
}

const populateForm = (user) => {
  if (user) {
    form.firstName = user.firstName || ''
    form.lastName = user.lastName || ''
    form.username = user.username || ''
    form.email = user.email || ''
    form.role = user.role || ''
    form.status = user.status || 'active'
  }
}

const validateForm = () => {
  // Reset errors
  Object.keys(errors).forEach(key => {
    errors[key] = ''
  })

  let isValid = true

  // First name validation
  if (!form.firstName.trim()) {
    errors.firstName = 'First name is required'
    isValid = false
  }

  // Last name validation
  if (!form.lastName.trim()) {
    errors.lastName = 'Last name is required'
    isValid = false
  }

  // Username validation
  if (!form.username.trim()) {
    errors.username = 'Username is required'
    isValid = false
  } else if (form.username.length < 3) {
    errors.username = 'Username must be at least 3 characters'
    isValid = false
  }

  // Email validation
  if (!form.email.trim()) {
    errors.email = 'Email is required'
    isValid = false
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    errors.email = 'Please enter a valid email address'
    isValid = false
  }

  // Password validation (only for new users)
  if (!isEditing.value) {
    if (!form.password) {
      errors.password = 'Password is required'
      isValid = false
    } else if (form.password.length < 8) {
      errors.password = 'Password must be at least 8 characters'
      isValid = false
    }

    if (!form.confirmPassword) {
      errors.confirmPassword = 'Please confirm your password'
      isValid = false
    } else if (form.password !== form.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match'
      isValid = false
    }
  }

  // Role validation
  if (!form.role) {
    errors.role = 'Role is required'
    isValid = false
  }

  // Status validation
  if (!form.status) {
    errors.status = 'Status is required'
    isValid = false
  }

  return isValid
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true
  submitError.value = ''

  try {
    const userData = {
      firstName: form.firstName.trim(),
      lastName: form.lastName.trim(),
      username: form.username.trim(),
      email: form.email.trim(),
      role: form.role,
      status: form.status
    }

    if (!isEditing.value) {
      userData.password = form.password
    }

    let result
    if (isEditing.value) {
      result = await apiService.users.update(props.user.id, userData)
      emit('user-updated', result.data)
      toast.success('User updated successfully!')
    } else {
      result = await apiService.users.create(userData)
      emit('user-created', result.data)
      toast.success('User created successfully!')
    }

    handleClose()
  } catch (error) {
    console.error('Error saving user:', error)
    submitError.value = error.response?.data?.message || 'An error occurred while saving the user'
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  isOpen.value = false
  resetForm()
}

// Watch for user prop changes
watch(() => props.user, (newUser) => {
  if (newUser) {
    populateForm(newUser)
  } else {
    resetForm()
  }
}, { immediate: true })

// Watch for modal open/close
watch(isOpen, (newValue) => {
  if (newValue && props.user) {
    populateForm(props.user)
  } else if (!newValue) {
    resetForm()
  }
})
</script>
