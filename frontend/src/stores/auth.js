/**
 * Pinia Authentication Store
 * Manages authentication state and actions
 */

import { defineStore } from 'pinia';
import authService from '@/services/auth.js';

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: authService.getUser(),
    isAuthenticated: authService.isAuthenticated(),
    loading: false,
    error: null
  }),

  getters: {
    /**
     * Get authentication token
     */
    token: () => authService.getToken(),

    /**
     * Get user's full name
     */
    userFullName: (state) => {
      if (!state.user) return '';
      const { firstName, middleName, lastName } = state.user;
      return [firstName, middleName, lastName].filter(Boolean).join(' ');
    },

    /**
     * Get user's role name
     */
    userRole: (state) => state.user?.roleName || '',

    /**
     * Check if user is admin
     */
    isAdmin: (state) => state.user?.roleName?.toLowerCase() === 'admin',

    /**
     * Check if user is manager
     */
    isManager: (state) => state.user?.roleName?.toLowerCase() === 'manager',

    /**
     * Check if user is regular user
     */
    isUser: (state) => state.user?.roleName?.toLowerCase() === 'user',

    /**
     * Check if user is manager or admin
     */
    isManagerOrAdmin: (state) => {
      const role = state.user?.roleName?.toLowerCase();
      return role === 'admin' || role === 'manager';
    }
  },

  actions: {
    /**
     * Login user
     */
    async login(email, password) {
      this.loading = true;
      this.error = null;

      try {
        const user = await authService.login(email, password);
        this.user = user;
        this.isAuthenticated = true;
        return user;
      } catch (error) {
        this.error = error.message;
        throw error;
      } finally {
        this.loading = false;
      }
    },

    /**
     * Register new user
     */
    async register(userData) {
      this.loading = true;
      this.error = null;

      try {
        const user = await authService.register(userData);
        this.user = user;
        this.isAuthenticated = true;
        return user;
      } catch (error) {
        this.error = error.message;
        throw error;
      } finally {
        this.loading = false;
      }
    },

    /**
     * Logout user
     */
    async logout() {
      this.loading = true;

      try {
        await authService.logout();
      } catch (error) {
        console.error('Logout error:', error);
      } finally {
        this.user = null;
        this.isAuthenticated = false;
        this.loading = false;
        this.error = null;
      }
    },

    /**
     * Refresh user data
     */
    async refreshUser() {
      if (!this.isAuthenticated) return;

      try {
        const user = await authService.getCurrentUser();
        this.user = user;
      } catch (error) {
        console.error('Failed to refresh user:', error);
        // If refresh fails, logout user
        await this.logout();
      }
    },

    /**
     * Verify authentication status
     */
    async verifyAuth() {
      if (!authService.isAuthenticated()) {
        this.user = null;
        this.isAuthenticated = false;
        return false;
      }

      try {
        const isValid = await authService.verifyToken();
        if (!isValid) {
          await this.logout();
          return false;
        }

        // Refresh user data if token is valid
        await this.refreshUser();
        return true;
      } catch (error) {
        await this.logout();
        return false;
      }
    },

    /**
     * Initialize auth state on app start
     */
    async initialize() {
      this.loading = true;

      try {
        if (authService.isAuthenticated()) {
          const isValid = await this.verifyAuth();
          if (isValid) {
            this.user = authService.getUser();
            this.isAuthenticated = true;
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        await this.logout();
      } finally {
        this.loading = false;
      }
    },

    /**
     * Clear error state
     */
    clearError() {
      this.error = null;
    },

    /**
     * Check if user has required role
     */
    hasRole(requiredRole) {
      return authService.hasRole(requiredRole);
    }
  }
});
