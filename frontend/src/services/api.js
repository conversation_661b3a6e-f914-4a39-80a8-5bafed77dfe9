/**
 * API Service
 * Centralized API communication service with request/response interceptors
 */

import axios from 'axios'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'
import {
  transformUserFromBackend,
  transformUserToBackend,
  transformTaskFromBackend,
  transformTaskToBackend,
  transformErrorMessage
} from '@/utils/dataTransforms'

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    const token = authStore.token
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    const toast = useToast()
    const authStore = useAuthStore()
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // Unauthorized - clear auth and redirect to login
          authStore.logout()
          toast.error('Session expired. Please login again.')
          break
          
        case 403:
          // Forbidden
          toast.error('You do not have permission to perform this action.')
          break
          
        case 404:
          // Not found
          toast.error('Resource not found.')
          break
          
        case 422:
          // Validation error
          if (data.errors) {
            // Handle validation errors
            const errorMessages = Object.values(data.errors).flat()
            errorMessages.forEach(message => toast.error(message))
          } else {
            toast.error(data.message || 'Validation error occurred.')
          }
          break
          
        case 500:
          // Server error
          toast.error('Internal server error. Please try again later.')
          break
          
        default:
          // Other errors - use transformation for better error messages
          toast.error(transformErrorMessage(error))
      }
    } else if (error.request) {
      // Network error
      toast.error('Network error. Please check your connection.')
    } else {
      // Other error
      toast.error('An unexpected error occurred.')
    }
    
    return Promise.reject(error)
  }
)

// API methods
export const apiService = {
  // Generic methods
  get: (url, config = {}) => api.get(url, config),
  post: (url, data = {}, config = {}) => api.post(url, data, config),
  put: (url, data = {}, config = {}) => api.put(url, data, config),
  patch: (url, data = {}, config = {}) => api.patch(url, data, config),
  delete: (url, config = {}) => api.delete(url, config),
  
  // Authentication
  auth: {
    login: (credentials) => api.post('/auth/login', credentials),
    register: (userData) => api.post('/auth/register', userData),
    logout: () => api.post('/auth/logout'),
    refreshToken: () => api.post('/auth/refresh'),
    me: () => api.get('/auth/me')
  },
  
  // Users
  users: {
    getAll: (params = {}) => api.get('/users', { params }),
    getById: (id) => api.get(`/users/${id}`),
    create: (userData) => {
      // Map frontend role names to backend roleId
      const roleMap = { admin: 1, user: 2, manager: 3 }
      const backendData = {
        ...userData,
        roleId: roleMap[userData.role] || 2 // default to user
      }
      delete backendData.role
      delete backendData.status // backend doesn't use status in create
      return api.post('/users', backendData)
    },
    update: (id, userData) => {
      // Map frontend role names to backend roleId
      const roleMap = { admin: 1, user: 2, manager: 3 }
      const backendData = {
        ...userData,
        roleId: roleMap[userData.role] || userData.roleId
      }
      delete backendData.role
      delete backendData.status // backend doesn't use status in update
      return api.put(`/users/${id}`, backendData)
    },
    delete: (id) => api.delete(`/users/${id}`),
    updateProfile: (userData) => api.put('/users/profile', userData),
    changePassword: (passwordData) => api.put('/users/change-password', passwordData)
  },
  
  // Tasks
  tasks: {
    getAll: (params = {}) => api.get('/tasks', { params }),
    getById: (id) => api.get(`/tasks/${id}`),
    create: (taskData) => {
      // Map frontend data to backend structure
      const backendData = {
        title: taskData.title,
        description: taskData.description || '',
        priority: taskData.priority || 1,
        deadline: taskData.deadline,
        assignedUsers: taskData.assignedUsers || []
      }
      return api.post('/tasks', backendData)
    },
    update: (id, taskData) => {
      // Map frontend status to backend status
      const statusMap = {
        'pending': 'pending',
        'in_progress': 'inprogress',
        'completed': 'completed',
        'cancelled': 'archived'
      }
      const backendData = {
        ...taskData,
        status: statusMap[taskData.status] || taskData.status
      }
      return api.put(`/tasks/${id}`, backendData)
    },
    delete: (id) => api.delete(`/tasks/${id}`),
    updateStatus: (id, status) => {
      const statusMap = {
        'pending': 'pending',
        'in_progress': 'inprogress',
        'completed': 'completed',
        'cancelled': 'archived'
      }
      return api.patch(`/tasks/${id}/status`, { status: statusMap[status] || status })
    },
    assign: (id, userIds) => api.patch(`/tasks/${id}/assign`, { userIds }),
    getMyTasks: () => api.get('/tasks/my-tasks'),
    getStats: () => api.get('/tasks/stats')
  },
  
  // Notifications
  notifications: {
    getAll: (params = {}) => api.get('/notifications', { params }),
    markAsRead: (id) => api.patch(`/notifications/${id}/read`),
    markAllAsRead: () => api.patch('/notifications/mark-all-read'),
    delete: (id) => api.delete(`/notifications/${id}`)
  }
}

export default api
