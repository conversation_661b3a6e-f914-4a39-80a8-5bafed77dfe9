/**
 * Authentication Debug Utilities
 * Helps debug authentication and token issues
 */

import { authService } from '@/services/auth'
import { apiService } from '@/services/api'

/**
 * Debug current authentication state
 */
export const debugAuthState = () => {
  console.log('🔍 Authentication Debug Info:')
  console.log('================================')
  
  // Check auth service state
  console.log('Auth Service State:')
  console.log('- Is Authenticated:', authService.isAuthenticated())
  console.log('- Access Token:', authService.getToken() ? 'Present' : 'Missing')
  console.log('- Refresh Token:', authService.refreshToken ? 'Present' : 'Missing')
  console.log('- User:', authService.getUser())
  
  // Check localStorage
  console.log('\nLocalStorage:')
  console.log('- accessToken:', localStorage.getItem('accessToken') ? 'Present' : 'Missing')
  console.log('- refreshToken:', localStorage.getItem('refreshToken') ? 'Present' : 'Missing')
  console.log('- user:', localStorage.getItem('user') ? 'Present' : 'Missing')
  
  // Check token expiry
  const token = authService.getToken()
  if (token) {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      const now = Math.floor(Date.now() / 1000)
      const isExpired = payload.exp < now
      const timeLeft = payload.exp - now
      
      console.log('\nToken Info:')
      console.log('- Expires at:', new Date(payload.exp * 1000).toLocaleString())
      console.log('- Is Expired:', isExpired)
      console.log('- Time left:', isExpired ? 'Expired' : `${Math.floor(timeLeft / 60)} minutes`)
      console.log('- User ID:', payload.userId)
      console.log('- Role ID:', payload.roleId)
    } catch (error) {
      console.log('- Token parsing error:', error.message)
    }
  }
  
  console.log('================================')
}

/**
 * Test API call with current token
 */
export const testApiCall = async () => {
  console.log('🧪 Testing API Call...')
  
  try {
    const response = await apiService.users.getAll()
    console.log('✅ API call successful:', response.data)
    return true
  } catch (error) {
    console.log('❌ API call failed:', error.message)
    console.log('Error details:', error.response?.data)
    return false
  }
}

/**
 * Test token refresh
 */
export const testTokenRefresh = async () => {
  console.log('🔄 Testing Token Refresh...')
  
  try {
    const newToken = await authService.refreshAccessToken()
    console.log('✅ Token refresh successful')
    console.log('New token:', newToken ? 'Present' : 'Missing')
    return true
  } catch (error) {
    console.log('❌ Token refresh failed:', error.message)
    return false
  }
}

/**
 * Force token expiry for testing
 */
export const forceTokenExpiry = () => {
  console.log('⚠️ Forcing token expiry for testing...')
  
  const token = authService.getToken()
  if (token) {
    try {
      const parts = token.split('.')
      const payload = JSON.parse(atob(parts[1]))
      
      // Set expiry to 1 second ago
      payload.exp = Math.floor(Date.now() / 1000) - 1
      
      // Create new token with expired payload
      const expiredToken = parts[0] + '.' + btoa(JSON.stringify(payload)) + '.' + parts[2]
      
      // Update in auth service and localStorage
      authService.accessToken = expiredToken
      localStorage.setItem('accessToken', expiredToken)
      
      console.log('✅ Token expired artificially')
      debugAuthState()
    } catch (error) {
      console.log('❌ Error forcing expiry:', error.message)
    }
  } else {
    console.log('❌ No token to expire')
  }
}

/**
 * Run comprehensive auth tests
 */
export const runAuthTests = async () => {
  console.log('🚀 Running Comprehensive Auth Tests...')
  console.log('=====================================')
  
  // 1. Check current state
  debugAuthState()
  
  // 2. Test API call
  const apiSuccess = await testApiCall()
  
  // 3. Test token refresh if API failed
  if (!apiSuccess) {
    console.log('\n🔄 API failed, testing token refresh...')
    const refreshSuccess = await testTokenRefresh()
    
    if (refreshSuccess) {
      console.log('\n🔄 Retrying API call after refresh...')
      await testApiCall()
    }
  }
  
  console.log('\n✅ Auth tests completed')
  console.log('=====================================')
}

/**
 * Clear all auth data for testing
 */
export const clearAuthData = () => {
  console.log('🗑️ Clearing all auth data...')
  authService.clearAuthData()
  console.log('✅ Auth data cleared')
}

// Export for browser console
if (typeof window !== 'undefined') {
  window.authDebug = {
    state: debugAuthState,
    testApi: testApiCall,
    testRefresh: testTokenRefresh,
    forceExpiry: forceTokenExpiry,
    runTests: runAuthTests,
    clear: clearAuthData
  }
  
  console.log('🔧 Auth debug tools available at window.authDebug')
}
